import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import Session from 'supertokens-web-js/recipe/session'

export const requireAuth = async (
  _to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext
): Promise<void> => {
  if (!(await Session.doesSessionExist())) {
    next('/auth')
  } else {
    next()
  }
}

export const requireGuest = async (
  _to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext
): Promise<void> => {
  if (await Session.doesSessionExist()) {
    next('/dashboard')
  } else {
    next()
  }
}
