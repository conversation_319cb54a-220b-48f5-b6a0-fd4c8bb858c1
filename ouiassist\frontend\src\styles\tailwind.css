@import "tailwindcss";

/* Custom component classes using standard CSS */
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #eff6ff 0%, white 50%, #dbeafe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

@media (min-width: 640px) {
  .profile-container {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .profile-container {
    padding: 2rem;
  }
}

.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-radius: 1rem;
  overflow: hidden;
  animation: scaleIn 0.3s ease-out;
}

.form-section {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .form-section {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .form-section {
    padding: 2.5rem;
  }
}

.header-section {
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
  padding: 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

@media (min-width: 640px) {
  .header-section {
    padding: 2rem;
  }
}

.header-pattern {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.profile-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  position: relative;
  z-index: 10;
  animation: fadeIn 0.5s ease-in-out;
  margin: 0;
}

@media (min-width: 640px) {
  .profile-title {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .profile-title {
    font-size: 2.25rem;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  animation: slideUp 0.4s ease-out;
}

.form-field-full {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  animation: slideUp 0.4s ease-out;
}

@media (min-width: 768px) {
  .form-field-full {
    grid-column: span 2;
  }
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-top: 1rem;
}

@media (min-width: 640px) {
  .button-container {
    flex-direction: row;
    gap: 1rem;
  }
}

.btn-primary {
  flex: 1;
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(90deg, #1d4ed8 0%, #1e40af 100%);
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: scale(1);
}

.btn-secondary {
  flex: 1;
  background: white;
  border: 2px solid #cbd5e1;
  color: #334155;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  border-color: #94a3b8;
  background: #f8fafc;
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(203, 213, 225, 0.3);
}

.loading-spinner {
  animation: spin 1s linear infinite;
  height: 1.25rem;
  width: 1.25rem;
  margin-right: 0.5rem;
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

.enhanced-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-input:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Note: Keyframe animations are defined in tailwind.config.js */

/* Animation delays for staggered effects */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }

/* Form label styling */
.n-form-item-label {
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.5rem;
}

/* Form input focus states */
.n-input:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.n-input--textarea:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive font sizes */
@media (max-width: 640px) {
  .profile-title {
    font-size: 1.25rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .button-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .glass-card {
    margin: 0.5rem;
    border-radius: 0.75rem;
  }

  .header-section {
    padding: 1.5rem;
  }

  .form-section {
    padding: 1rem;
  }
}

/* Medium screens and up */
@media (min-width: 768px) {
  .glass-card {
    margin: 1rem;
  }

  .header-section {
    padding: 2rem;
  }

  .form-section {
    padding: 1.5rem;
  }
}

/* Large screens and up */
@media (min-width: 1024px) {
  .glass-card {
    margin: 1.5rem;
  }

  .header-section {
    padding: 2.5rem;
  }

  .form-section {
    padding: 2rem;
  }
}
