@import "tailwindcss";

/* Custom component classes using Tailwind utilities */
.profile-container {
  @apply min-h-screen flex items-center justify-center p-4 sm:p-6 lg:p-8;
  background: linear-gradient(135deg, #eff6ff 0%, white 50%, #dbeafe 100%);
}

.glass-card {
  @apply rounded-2xl overflow-hidden;
  @apply m-2 sm:m-4 lg:m-6;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: scaleIn 0.3s ease-out;
}

.form-section {
  @apply flex flex-col gap-6 p-4 sm:p-6 lg:p-8;
}

.header-section {
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
  padding: 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

@media (min-width: 640px) {
  .header-section {
    padding: 2rem;
  }
}

.header-pattern {
  position: absolute;
  inset: 0;
  opacity: 0.1;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.profile-title {
  @apply text-xl sm:text-3xl lg:text-4xl font-bold text-white relative z-10 m-0;
  animation: fadeIn 0.5s ease-in-out;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6;
}

.form-field {
  @apply flex flex-col gap-2;
  animation: slideUp 0.4s ease-out;
}

.form-field-full {
  @apply flex flex-col gap-2 md:col-span-2;
  animation: slideUp 0.4s ease-out;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-top: 1rem;
}

@media (min-width: 640px) {
  .button-container {
    flex-direction: row;
    gap: 1rem;
  }
}

.btn-primary {
  flex: 1;
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(90deg, #1d4ed8 0%, #1e40af 100%);
  transform: scale(1.05);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: scale(1);
}

.btn-secondary {
  flex: 1;
  background: white;
  border: 2px solid #cbd5e1;
  color: #334155;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  border-color: #94a3b8;
  background: #f8fafc;
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(203, 213, 225, 0.3);
}

.loading-spinner {
  animation: spin 1s linear infinite;
  height: 1.25rem;
  width: 1.25rem;
  margin-right: 0.5rem;
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

.enhanced-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-input:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Note: Keyframe animations are defined in tailwind.config.js */

/* Animation delays for staggered effects */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }

/* Form label styling */
.n-form-item-label {
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.5rem;
}

/* Form input focus states */
.n-input:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.n-input--textarea:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive adjustments for small screens */
@media (max-width: 640px) {
  .button-container {
    @apply flex-col gap-2;
  }
}
