export interface PersonName {
  first_name: string
  last_name: string
  middle_name?: string
}

export interface UserProfile {
  id: string
  authentication_id: string
  name: PersonName
  phone_number?: string
  avatar_url?: string
  bio?: string
  created_at: string
  updated_at: string
}

export interface ProfileFormData {
  first_name: string
  last_name: string
  middle_name?: string
  phone_number?: string
  bio?: string
  avatar_url?: string
}

export interface ProfileCompletionStatus {
  exists: boolean
  isComplete: boolean
  isMinimal: boolean
  profile?: UserProfile
}

export interface ApiResponse<T = any> {
  status: 'success' | 'error'
  data?: T
  message?: string
}

export interface ProfileFormProps {
  initialData?: Partial<ProfileFormData>
  isUpdate?: boolean
  loading?: boolean
}

export interface ProfileFormEmits {
  (e: 'submit', data: ProfileFormData): void
  (e: 'skip'): void
}
