<template>
  <NForm ref="formRef" :model="formData" :rules="rules" @submit.prevent="onSubmit" class="space-y-6">
    <!-- Name Fields Grid -->
    <div class="form-grid">
      <div class="form-field animate-slide-up">
        <NFormItem path="first_name" :label="labels.fields.firstName" class="w-full">
          <NInput v-model:value="formData.first_name" :placeholder="labels.fields.firstName"
            class="enhanced-input w-full" size="large" />
        </NFormItem>
      </div>

      <div class="form-field animate-slide-up animate-delay-100">
        <NFormItem path="last_name" :label="labels.fields.lastName" class="w-full">
          <NInput v-model:value="formData.last_name" :placeholder="labels.fields.lastName" class="enhanced-input w-full"
            size="large" />
        </NFormItem>
      </div>
    </div>

    <!-- Middle Name Field -->
    <div class="form-field-full animate-slide-up animate-delay-200">
      <NFormItem path="middle_name" :label="labels.fields.middleName" class="w-full">
        <NInput v-model:value="formData.middle_name" :placeholder="labels.fields.middleName"
          class="enhanced-input w-full" size="large" />
      </NFormItem>
    </div>

    <!-- Phone Number Field -->
    <div class="form-field-full animate-slide-up animate-delay-300">
      <NFormItem path="phone_number" :label="labels.fields.phoneNumber" class="w-full">
        <NInput v-model:value="formData.phone_number" :placeholder="labels.fields.phoneNumber"
          class="enhanced-input w-full" size="large" />
      </NFormItem>
    </div>

    <!-- Bio Field -->
    <div class="form-field-full animate-slide-up animate-delay-400">
      <NFormItem path="bio" :label="labels.fields.bio" class="w-full">
        <NInput v-model:value="formData.bio" type="textarea" :placeholder="labels.fields.bio" :rows="3"
          class="enhanced-input w-full" size="large" />
      </NFormItem>
    </div>

    <!-- Avatar URL Field -->
    <div class="form-field-full animate-slide-up animate-delay-500">
      <NFormItem path="avatar_url" :label="labels.fields.avatarUrl" class="w-full">
        <NInput v-model:value="formData.avatar_url" :placeholder="labels.fields.avatarUrl" class="enhanced-input w-full"
          size="large" />
      </NFormItem>
    </div>

    <!-- Action Buttons -->
    <div class="button-container animate-slide-up animate-delay-600">
      <button type="submit" :disabled="!isFormValid || loading"
        class="btn-primary focus:outline-none focus:ring-4 focus:ring-french-blue-300 disabled:opacity-50 disabled:cursor-not-allowed"
        :aria-label="isUpdate ? labels.buttons.update : labels.buttons.complete">
        <svg v-if="loading" class="loading-spinner" fill="none" viewBox="0 0 24 24" aria-hidden="true">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
          </path>
        </svg>
        <span>{{ isUpdate ? labels.buttons.update : labels.buttons.complete }}</span>
      </button>

      <button v-if="!isUpdate" type="button" :disabled="loading" @click="onSkip"
        class="btn-secondary focus:outline-none focus:ring-4 focus:ring-elegant-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
        :aria-label="labels.buttons.skip">
        {{ labels.buttons.skip }}
      </button>
    </div>
  </NForm>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  NForm,
  NFormItem,
  NInput,
  FormInst
} from 'naive-ui'
import { ProfileFormData, ProfileFormProps, ProfileFormEmits } from '../types/profile'
import { frenchLabels } from '../locales/fr'
import { createValidationRules, createFormRules, validateFormData } from '../utils/validation'

const props = withDefaults(defineProps<ProfileFormProps>(), {
  initialData: () => ({}),
  isUpdate: false,
  loading: false
})

const emit = defineEmits<ProfileFormEmits>()

const formRef = ref<FormInst | null>(null)
const labels = frenchLabels.profile

const formData = reactive<ProfileFormData>({
  first_name: '',
  last_name: '',
  middle_name: '',
  phone_number: '',
  bio: '',
  avatar_url: ''
})

const rules = createFormRules<ProfileFormData>({
  first_name: createValidationRules.requiredName('Prénom'),
  last_name: createValidationRules.requiredName('Nom'),
  middle_name: createValidationRules.optionalName(),
  phone_number: createValidationRules.phone(),
  bio: createValidationRules.bio(500),
  avatar_url: createValidationRules.url(false)
})

const isFormValid = computed(() => {
  return formData.first_name.length >= 2 && formData.last_name.length >= 2
})

watch(() => props.initialData, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}, { immediate: true })

async function onSubmit() {
  await validateFormData(
    formRef.value,
    () => emit('submit', { ...formData })
  )
}

function onSkip() {
  emit('skip')
}
</script>
