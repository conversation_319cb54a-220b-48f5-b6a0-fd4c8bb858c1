export interface ErrorHandlerOptions {
  logPrefix?: string
  fallbackValue?: any
  showUserMessage?: boolean
  rethrow?: boolean
}

export interface ApiErrorResponse {
  status: 'error'
  message?: string
  data?: any
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: ApiErrorResponse
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export class NetworkError extends Error {
  constructor(message: string, public originalError: Error) {
    super(message)
    this.name = 'NetworkError'
  }
}

export async function handleApiCall<T>(
  apiCall: () => Promise<Response>,
  options: ErrorHandlerOptions = {}
): Promise<T | null> {
  const { logPrefix = 'API Error', fallbackValue = null, rethrow = false } = options

  try {
    const response = await apiCall()

    if (!response.ok) {
      let errorData: ApiErrorResponse | null = null
      try {
        errorData = await response.json()
      } catch {
        // Response body is not JSON
      }

      const errorMessage = errorData?.message || `HTTP ${response.status}: ${response.statusText}`
      console.error(`${logPrefix}:`, errorMessage, errorData)

      if (rethrow) {
        throw new ApiError(errorMessage, response.status, errorData || undefined)
      }

      return fallbackValue
    }

    return await response.json()
  } catch (error) {
    if (error instanceof ApiError && rethrow) {
      throw error
    }

    const networkError = new NetworkError(
      'Network request failed',
      error as Error
    )

    console.error(`${logPrefix}:`, networkError.message, error)

    if (rethrow) {
      throw networkError
    }

    return fallbackValue
  }
}

export function handleAsyncError<T>(
  asyncFn: () => Promise<T>,
  options: ErrorHandlerOptions = {}
): Promise<T | null> {
  const { logPrefix = 'Async Error', fallbackValue = null, rethrow = false } = options

  return asyncFn().catch((error) => {
    console.error(`${logPrefix}:`, error)

    if (rethrow) {
      throw error
    }

    return fallbackValue
  })
}

export function logError(message: string, error: unknown, context?: string): void {
  const prefix = context ? `[${context}]` : ''
  console.error(`${prefix} ${message}:`, error)
}

export function createDefaultErrorHandler<T>(
  fallbackValue: T,
  logPrefix: string
) {
  return (error: unknown): T => {
    logError('Operation failed', error, logPrefix)
    return fallbackValue
  }
}
