import { getApiDomain } from '../config'
import { ProfileCompletionStatus, ApiResponse } from '../types/profile'
import { handleApiCall } from './errorHandler'

export async function checkProfileCompletion(): Promise<ProfileCompletionStatus> {
  const defaultStatus: ProfileCompletionStatus = {
    exists: false,
    isComplete: false,
    isMinimal: false
  }

  const result = await handleApiCall<ApiResponse<ProfileCompletionStatus>>(
    () => fetch(`${getApiDomain()}/profile/check`, {
      credentials: 'include'
    }),
    {
      logPrefix: 'Erreur lors de la vérification du profil',
      fallbackValue: null
    }
  )

  if (result?.data) {
    return result.data
  }

  return defaultStatus
}

export function getProfileCompletionRedirectUrl(profileStatus: ProfileCompletionStatus): string {
  if (!profileStatus.exists) {
    return '/profile-completion?mode=create'
  }

  if (profileStatus.isMinimal || !profileStatus.isComplete) {
    return '/profile-completion?mode=update'
  }

  return '/dashboard'
}
