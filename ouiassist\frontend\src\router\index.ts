import { createRouter, createWebHistory } from "vue-router";
import HomeView from "../views/HomeView.vue";
import AuthView from "../views/AuthView.vue";
import DashboardView from "../views/DashboardView.vue";
import ProfileCompletionView from "../views/ProfileCompletionView.vue";
import { requireAuth } from "../utils/routeGuards";

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: "/",
            name: "home",
            component: HomeView,
        },
        {
            path: "/auth/:pathMatch(.*)*",
            name: "auth",
            component: AuthView,
        },
        {
            path: "/profile-completion",
            name: "profile-completion",
            component: ProfileCompletionView,
            beforeEnter: requireAuth,
        },
        {
            path: "/dashboard",
            name: "dashboard",
            component: DashboardView,
            beforeEnter: requireAuth,
        },
    ],
});

export default router;
