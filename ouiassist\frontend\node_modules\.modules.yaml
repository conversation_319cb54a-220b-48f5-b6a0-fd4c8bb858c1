hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@css-render/plugin-bem@0.15.14(css-render@0.15.14)':
    '@css-render/plugin-bem': private
  '@css-render/vue3-ssr@0.15.14(vue@3.5.17(typescript@5.6.3))':
    '@css-render/vue3-ssr': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': private
  '@primeuix/styled@0.7.0':
    '@primeuix/styled': private
  '@primeuix/styles@1.2.1':
    '@primeuix/styles': private
  '@primeuix/utils@0.6.0':
    '@primeuix/utils': private
  '@primevue/core@4.3.6(vue@3.5.17(typescript@5.6.3))':
    '@primevue/core': private
  '@primevue/icons@4.3.6(vue@3.5.17(typescript@5.6.3))':
    '@primevue/icons': private
  '@rollup/rollup-win32-x64-msvc@4.44.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@simplewebauthn/browser@13.1.2':
    '@simplewebauthn/browser': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@volar/language-core@2.4.15':
    '@volar/language-core': private
  '@volar/source-map@2.4.15':
    '@volar/source-map': private
  '@volar/typescript@2.4.15':
    '@volar/typescript': private
  '@vue/compiler-core@3.5.17':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.17':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.17':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.17':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/language-core@2.2.12(typescript@5.6.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.17':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.17':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.17':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.17(vue@3.5.17(typescript@5.6.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.17':
    '@vue/shared': private
  alien-signals@1.0.13:
    alien-signals: private
  async-validator@4.2.5:
    async-validator: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@2.0.2:
    brace-expansion: private
  browserslist@4.25.1:
    browserslist: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chownr@3.0.0:
    chownr: private
  css-render@0.15.14:
    css-render: private
  csstype@3.1.3:
    csstype: private
  date-fns-tz@3.2.0(date-fns@3.6.0):
    date-fns-tz: private
  de-indent@1.0.2:
    de-indent: private
  detect-libc@2.0.4:
    detect-libc: private
  electron-to-chromium@1.5.182:
    electron-to-chromium: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  escalade@3.2.0:
    escalade: private
  estree-walker@2.0.2:
    estree-walker: private
  evtd@0.2.4:
    evtd: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fraction.js@4.3.7:
    fraction.js: private
  graceful-fs@4.2.11:
    graceful-fs: private
  he@1.2.0:
    he: private
  highlight.js@11.11.1:
    highlight.js: private
  jiti@2.4.2:
    jiti: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash@4.17.21:
    lodash: private
  magic-string@0.30.17:
    magic-string: private
  minimatch@9.0.5:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  muggle-string@0.4.1:
    muggle-string: private
  nanoid@3.3.11:
    nanoid: private
  node-releases@2.0.19:
    node-releases: private
  normalize-range@0.1.2:
    normalize-range: private
  path-browserify@1.0.1:
    path-browserify: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  rollup@4.44.2:
    rollup: private
  seemly@0.3.10:
    seemly: private
  source-map-js@1.2.1:
    source-map-js: private
  supertokens-js-override@0.0.4:
    supertokens-js-override: private
  supertokens-website@20.1.6:
    supertokens-website: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  tinyglobby@0.2.14:
    tinyglobby: private
  treemate@0.3.11:
    treemate: private
  undici-types@6.21.0:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  vdirs@0.1.8(vue@3.5.17(typescript@5.6.3)):
    vdirs: private
  vooks@0.2.12(vue@3.5.17(typescript@5.6.3)):
    vooks: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vueuc@0.4.64(vue@3.5.17(typescript@5.6.3)):
    vueuc: private
  yallist@5.0.0:
    yallist: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Fri, 11 Jul 2025 09:01:24 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.44.2'
  - '@rollup/rollup-android-arm64@4.44.2'
  - '@rollup/rollup-darwin-arm64@4.44.2'
  - '@rollup/rollup-darwin-x64@4.44.2'
  - '@rollup/rollup-freebsd-arm64@4.44.2'
  - '@rollup/rollup-freebsd-x64@4.44.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.2'
  - '@rollup/rollup-linux-arm64-gnu@4.44.2'
  - '@rollup/rollup-linux-arm64-musl@4.44.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-musl@4.44.2'
  - '@rollup/rollup-linux-s390x-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-musl@4.44.2'
  - '@rollup/rollup-win32-arm64-msvc@4.44.2'
  - '@rollup/rollup-win32-ia32-msvc@4.44.2'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\OuiLink\ouiassist\ouiassist\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
