<template>
  <NConfigProvider :locale="naiveUILocale">
    <!-- Main Container with Gradient Background -->
    <div class="profile-container" role="main" aria-labelledby="profile-title">
      <!-- Floating Background Elements -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none" aria-hidden="true">
        <div class="floating-element absolute top-20 left-10 w-32 h-32 bg-french-blue-200/30 rounded-full blur-xl">
        </div>
        <div
          class="floating-element absolute top-40 right-20 w-24 h-24 bg-french-red-200/20 rounded-full blur-lg animate-delay-200">
        </div>
        <div
          class="floating-element absolute bottom-32 left-1/4 w-40 h-40 bg-french-blue-300/20 rounded-full blur-2xl animate-delay-400">
        </div>
      </div>

      <!-- Main Card Container -->
      <div class="relative w-full max-w-2xl mx-auto">
        <!-- Glass Card with Modern Design -->
        <div class="glass-card" role="form" aria-labelledby="profile-title">
          <!-- Header Section with Gradient -->
          <div class="header-section">
            <!-- Background Pattern -->
            <div class="header-pattern" aria-hidden="true"></div>

            <!-- Title with Animation -->
            <h1 id="profile-title" class="profile-title">
              {{ isUpdate ? labels.title.update : labels.title.complete }}
            </h1>

            <!-- Subtitle -->
            <p class="text-french-blue-100 text-sm sm:text-base mt-2 relative z-10 animate-fade-in animate-delay-200">
              {{ isUpdate ? labels.subtitle.update : labels.subtitle.complete }}
            </p>
          </div>

          <!-- Form Section -->
          <div class="form-section">
            <ProfileForm :initial-data="profileData" :is-update="isUpdate" :loading="loading" @submit="handleSubmit"
              @skip="handleSkip" />
          </div>
        </div>

        <!-- Additional Visual Elements -->
        <div
          class="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-br from-french-blue-400 to-french-blue-600 rounded-full opacity-60 animate-pulse"
          aria-hidden="true">
        </div>
        <div
          class="absolute -bottom-2 -left-2 w-6 h-6 bg-gradient-to-br from-french-red-400 to-french-red-600 rounded-full opacity-40 animate-pulse animate-delay-300"
          aria-hidden="true">
        </div>
      </div>
    </div>
  </NConfigProvider>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NConfigProvider } from 'naive-ui'
import ProfileForm from '../components/ProfileForm.vue'
import { getApiDomain } from '../config'
import { naiveUILocale, frenchLabels } from '../locales'
import { ProfileFormData, ProfileCompletionStatus } from '../types/profile'
import { handleApiCall } from '../utils/errorHandler'

const route = useRoute()
const router = useRouter()

const isUpdate = ref(false)
const profileData = ref<Partial<ProfileFormData>>({})
const loading = ref(false)
const labels = frenchLabels.profile

onMounted(async () => {
  const mode = route.query.mode as string
  isUpdate.value = mode === 'update'

  if (isUpdate.value) {
    await loadExistingProfile()
  }
})

async function loadExistingProfile() {
  const result = await handleApiCall<ProfileCompletionStatus>(
    () => fetch(`${getApiDomain()}/profile/check`, {
      credentials: 'include'
    }),
    {
      logPrefix: 'Erreur lors du chargement du profil',
      fallbackValue: null
    }
  )

  if (result?.exists && result.profile) {
    profileData.value = {
      first_name: result.profile.name.first_name,
      last_name: result.profile.name.last_name,
      middle_name: result.profile.name.middle_name,
      phone_number: result.profile.phone_number,
      bio: result.profile.bio,
      avatar_url: result.profile.avatar_url
    }
  }
}

async function handleSubmit(formData: ProfileFormData) {
  loading.value = true

  try {
    const endpoint = isUpdate.value ? '/profile' : '/profile'
    const method = isUpdate.value ? 'PUT' : 'POST'

    const response = await fetch(`${getApiDomain()}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(formData)
    })

    if (response.ok) {
      router.push('/dashboard')
    } else {
      const error = await response.json()
      console.error(labels.messages.submitError, error)
    }
  } catch (error) {
    console.error(labels.messages.networkError, error)
  } finally {
    loading.value = false
  }
}

async function handleSkip() {
  if (!isUpdate.value) {
    loading.value = true

    try {
      const response = await fetch(`${getApiDomain()}/profile/minimal`, {
        method: 'POST',
        credentials: 'include'
      })

      if (response.ok) {
        router.push('/dashboard')
      }
    } catch (error) {
      console.error(labels.messages.minimalProfileError, error)
    } finally {
      loading.value = false
    }
  }
}
</script>

<!-- Styling now handled by Tailwind CSS classes and custom components -->
