import type { FormItemRule } from 'naive-ui'
import { frenchLabels } from '../locales/fr'

export interface ValidationOptions {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  message?: string
  trigger?: string | string[]
}

export class ValidationBuilder {
  private rules: FormItemRule[] = []

  required(message?: string): ValidationBuilder {
    this.rules.push({
      required: true,
      message: message || frenchLabels.profile.validation.required,
      trigger: ['blur', 'input']
    })
    return this
  }

  minLength(min: number, message?: string): ValidationBuilder {
    this.rules.push({
      min,
      message: message || frenchLabels.profile.validation.minLength.replace('{min}', min.toString()),
      trigger: 'blur'
    })
    return this
  }

  maxLength(max: number, message?: string): ValidationBuilder {
    this.rules.push({
      max,
      message: message || `Doit contenir au maximum ${max} caractères`,
      trigger: 'blur'
    })
    return this
  }

  pattern(regex: RegExp, message: string): ValidationBuilder {
    this.rules.push({
      pattern: regex,
      message,
      trigger: 'blur'
    })
    return this
  }

  custom(validator: (rule: any, value: any) => boolean | Promise<boolean>, message: string): ValidationBuilder {
    this.rules.push({
      validator: (rule, value) => {
        const result = validator(rule, value)
        if (result instanceof Promise) {
          return result.then(valid => valid ? Promise.resolve() : Promise.reject(new Error(message)))
        }
        return result ? Promise.resolve() : Promise.reject(new Error(message))
      },
      trigger: 'blur'
    })
    return this
  }

  build(): FormItemRule[] {
    return [...this.rules]
  }
}

// Common validation patterns
export const ValidationPatterns = {
  // Phone number validation (international format)
  phone: /^[\+]?[1-9][\d]{0,15}$/,

  // Email validation (basic)
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,

  // Name validation (letters, spaces, hyphens, apostrophes)
  name: /^[a-zA-ZÀ-ÿ\s\-']+$/,

  // URL validation (basic)
  url: /^https?:\/\/.+/
}

// Pre-built validation rule factories
export const createValidationRules = {
  // Required name field (first name, last name, etc.)
  requiredName: (fieldName?: string): FormItemRule[] => {
    return new ValidationBuilder()
      .required()
      .minLength(2)
      .pattern(ValidationPatterns.name, `${fieldName || 'Ce champ'} ne peut contenir que des lettres, espaces, tirets et apostrophes`)
      .build()
  },

  // Optional name field
  optionalName: (): FormItemRule[] => {
    return new ValidationBuilder()
      .minLength(2)
      .pattern(ValidationPatterns.name, 'Ce champ ne peut contenir que des lettres, espaces, tirets et apostrophes')
      .build()
  },

  // Phone number field
  phone: (): FormItemRule[] => {
    return new ValidationBuilder()
      .pattern(ValidationPatterns.phone, frenchLabels.profile.validation.invalidPhone)
      .build()
  },

  // Email field
  email: (required = false): FormItemRule[] => {
    const builder = new ValidationBuilder()
    if (required) {
      builder.required()
    }
    return builder
      .pattern(ValidationPatterns.email, 'Format d\'email invalide')
      .build()
  },

  // Bio/description field
  bio: (maxLength = 500): FormItemRule[] => {
    return new ValidationBuilder()
      .maxLength(maxLength)
      .build()
  },

  // URL field
  url: (required = false): FormItemRule[] => {
    const builder = new ValidationBuilder()
    if (required) {
      builder.required()
    }
    return builder
      .pattern(ValidationPatterns.url, 'URL invalide (doit commencer par http:// ou https://)')
      .build()
  },

  // Generic required field
  required: (message?: string): FormItemRule[] => {
    return new ValidationBuilder()
      .required(message)
      .build()
  },

  // Generic text field with min/max length
  text: (options: { required?: boolean; min?: number; max?: number } = {}): FormItemRule[] => {
    const builder = new ValidationBuilder()

    if (options.required) {
      builder.required()
    }

    if (options.min) {
      builder.minLength(options.min)
    }

    if (options.max) {
      builder.maxLength(options.max)
    }

    return builder.build()
  }
}

// Utility function to validate form data programmatically
export async function validateFormData(
  formRef: any,
  onSuccess: () => void,
  onError?: (error: any) => void
): Promise<void> {
  try {
    await formRef?.validate()
    onSuccess()
  } catch (error) {
    console.error('Validation échouée:', error)
    if (onError) {
      onError(error)
    }
  }
}

// Helper to create form rules object
export function createFormRules<T extends Record<string, any>>(
  rulesConfig: { [K in keyof T]?: FormItemRule[] }
): { [K in keyof T]: FormItemRule[] } {
  return rulesConfig as { [K in keyof T]: FormItemRule[] }
}
