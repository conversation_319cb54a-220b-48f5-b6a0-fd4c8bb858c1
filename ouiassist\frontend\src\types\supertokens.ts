export interface SuperTokensUIContext {
  action: string
  isNewUser?: boolean
  user?: {
    id: string
    email?: string
    timeJoined?: number
  }
}

export interface SuperTokensUIRedirectionContext {
  action: string
  isNewUser?: boolean
  redirectToPath?: string
}

export interface SuperTokensUIConfig {
  appInfo: {
    websiteDomain: string
    apiDomain: string
    appName: string
    websiteBasePath: string
    apiBasePath: string
  }
  recipeList: any[]
  getRedirectionURL?: (context: SuperTokensUIRedirectionContext) => Promise<string | undefined>
  onHandleEvent?: (context: SuperTokensUIContext) => Promise<void>
}

declare global {
  interface Window {
    supertokensUIInit: (containerId: string, config: SuperTokensUIConfig) => void
    supertokensUISession: {
      init: () => any
    }
    supertokensUIEmailPassword: {
      init: () => any
    }
  }
}
